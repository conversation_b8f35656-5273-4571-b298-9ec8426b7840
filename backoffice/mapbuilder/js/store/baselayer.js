
const state = {

    currentBaseLayer: 2,
    remainQuota: 0,
    picMode: false,
    tileDate: 0,
    overlay: null,
    signupDialog:false,
    exhaustedDialog:false,
    selectionDialog:false,
    baseLayers: [],
    tileUrl: null,
    cachedBounds: null,
    cachedHighResResults: null,
    cachedImagery: null,
    tileProvider: null,
    tileDateDisplay: null,
    fileOpenTrigger: false,
    // EagleView specific state
    eagleviewLayerId: null,
    eagleviewTileMatrixSet: null,
    eagleviewMinZoom: null,
    eagleviewMaxZoom: null

}
const mutations  = {
    ...VuexPathify.make.mutations(state),
    clearData(state) {
        //state.tileProvider = null;
        //state.tileUrl = null;
        state.cachedBounds = null;
        state.cachedHighResResults = null;
        //state.tileDateDisplay = null;
        state.fileOpenTrigger = false;
        state.cachedImagery = null;
        state.signupDialog = false;
        state.exhaustedDialog = false;
        state.selectionDialog = false;
        //state.tileDate = 0;
        state.picMode = false;
        state.overlay = null;
        // Clear EagleView specific state
        state.eagleviewLayerId = null;
        state.eagleviewTileMatrixSet = null;
        state.eagleviewMinZoom = null;
        state.eagleviewMaxZoom = null;
    },
    clearDataAll(state) {
        state.tileProvider = null;
        state.currentBaseLayer = 2;
        state.tileUrl = null;
        state.cachedBounds = null;
        state.cachedHighResResults = null;
        state.tileDateDisplay = null;
        state.fileOpenTrigger = false;
        state.cachedImagery = null;
        state.signupDialog = false;
        state.exhaustedDialog = false;
        state.selectionDialog = false;
        state.tileDate = 0;
        state.picMode = false;
        state.overlay = null;
        // Clear EagleView specific state
        state.eagleviewLayerId = null;
        state.eagleviewTileMatrixSet = null;
        state.eagleviewMinZoom = null;
        state.eagleviewMaxZoom = null;
    }
}

const getters = {
    ...VuexPathify.make.getters(state),
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
}



